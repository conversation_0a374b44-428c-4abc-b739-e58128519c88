package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"go-api-solve/internal/config"
	"go-api-solve/pkg/redis"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	fmt.Printf("Testing Redis connection with config:\n")
	fmt.Printf("Redis Host: %s:%s\n", cfg.Redis.Host, cfg.Redis.Port)
	fmt.Printf("Redis Password: %s\n", maskPassword(cfg.Redis.Password))
	fmt.Printf("Redis DB: %d\n", cfg.Redis.DB)
	fmt.Println(strings.Repeat("=", 50))

	// 测试Redis连接
	fmt.Println("Testing Redis connection...")
	if err := redis.InitRedis(cfg); err != nil {
		log.Printf("❌ Redis connection failed: %v", err)
		return
	}
	fmt.Println("✅ Redis connection successful!")

	// 测试Redis操作
	ctx := context.Background()
	testKey := "test:connection"
	testValue := "Hello Redis!"

	fmt.Println("Testing Redis SET operation...")
	if err := redis.Set(ctx, testKey, testValue, time.Minute); err != nil {
		log.Printf("❌ Redis SET failed: %v", err)
		redis.CloseRedis()
		return
	}
	fmt.Println("✅ Redis SET successful!")

	fmt.Println("Testing Redis GET operation...")
	result, err := redis.Get(ctx, testKey)
	if err != nil {
		log.Printf("❌ Redis GET failed: %v", err)
		redis.CloseRedis()
		return
	}
	if result != testValue {
		log.Printf("❌ Redis GET value mismatch: expected %s, got %s", testValue, result)
		redis.CloseRedis()
		return
	}
	fmt.Printf("✅ Redis GET successful! Value: %s\n", result)

	fmt.Println("Testing Redis DELETE operation...")
	if err := redis.Delete(ctx, testKey); err != nil {
		log.Printf("❌ Redis DELETE failed: %v", err)
		redis.CloseRedis()
		return
	}
	fmt.Println("✅ Redis DELETE successful!")

	// 关闭连接
	redis.CloseRedis()
	fmt.Println("🎉 Redis test completed successfully!")
}

// maskPassword 遮蔽密码显示
func maskPassword(password string) string {
	if len(password) <= 4 {
		return strings.Repeat("*", len(password))
	}
	return password[:2] + strings.Repeat("*", len(password)-4) + password[len(password)-2:]
}
