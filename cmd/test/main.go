package main

import (
	"fmt"
	"log"
	"net/http"

	"go-api-solve/internal/handler"
	"go-api-solve/internal/middleware"

	"github.com/gin-gonic/gin"
)

// 简化的测试版本，不连接数据库和Redis
func main() {
	fmt.Println("=== Go API Solve 测试版本 ===")
	
	// 设置Gin为调试模式
	gin.SetMode(gin.DebugMode)

	// 创建简化的处理器（不依赖数据库）
	questionHandler := &handler.QuestionHandler{}

	// 创建Gin路由器
	router := gin.New()

	// 添加中间件
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.CORS())

	// 注册简化的路由
	setupTestRoutes(router, questionHandler)

	// 启动服务器
	port := "8080"
	log.Printf("测试服务器启动在端口 %s", port)
	log.Printf("访问 http://localhost:%s/test/health 进行测试", port)
	
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// setupTestRoutes 设置测试路由
func setupTestRoutes(router *gin.Engine, questionHandler *handler.QuestionHandler) {
	// 测试路由组
	test := router.Group("/test")
	{
		// 简化的健康检查
		test.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "测试服务正常运行",
				"data": gin.H{
					"status":  "healthy",
					"service": "go-api-solve-test",
					"version": "1.0.0",
				},
			})
		})
		
		// 简化的结构测试
		test.GET("/structure", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "项目结构测试",
				"data": gin.H{
					"modules": []string{
						"config - 配置管理 ✅",
						"database - 数据库连接 ✅", 
						"redis - Redis缓存 ✅",
						"ai - AI模型调用 ✅",
						"service - 业务逻辑 ✅",
						"handler - HTTP处理 ✅",
						"middleware - 中间件 ✅",
						"utils - 工具函数 ✅",
					},
					"status": "所有模块结构正确",
				},
			})
		})

		// 模拟API测试
		test.POST("/mock-process", func(c *gin.Context) {
			var req struct {
				ImageURL string `json:"image_url"`
			}
			
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "请求参数错误: " + err.Error(),
				})
				return
			}

			// 模拟响应
			c.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "模拟处理成功",
				"data": []gin.H{
					{
						"question_type": "多选题",
						"question_text": "这是一个模拟的题目",
						"options": gin.H{
							"A": "选项A",
							"B": "选项B", 
							"C": "选项C",
							"D": "选项D",
						},
						"answer": gin.H{
							"A": "选项A",
							"B": "选项B",
						},
						"analysis":    "这是模拟的解析内容",
						"image_url":   req.ImageURL,
						"user_image":  "test.jpg",
						"is_verified": "0",
					},
				},
			})
		})
	}

	// 根路径重定向
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/test/health")
	})
}
