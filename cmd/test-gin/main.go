package main

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	fmt.Println("Starting Gin HTTP server...")
	
	// 设置Gin为调试模式
	gin.SetMode(gin.DebugMode)
	
	// 创建Gin路由器
	router := gin.Default()
	
	// 健康检查路由
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"message": "Gin server is running",
			"service": "go-api-solve-test",
		})
	})
	
	// 根路径
	router.GET("/", func(c *gin.Context) {
		c.J<PERSON>(http.StatusOK, gin.H{
			"message": "Welcome to Go API Solve",
		})
	})
	
	port := "8080"
	fmt.Printf("Gin server starting on port %s\n", port)
	fmt.Printf("Visit http://localhost:%s/health to test\n", port)
	
	// 启动服务器
	if err := router.Run(":" + port); err != nil {
		fmt.Printf("Failed to start server: %v\n", err)
	}
}
