package main

import (
	"fmt"
	"log"
	"net/http"
)

func main() {
	fmt.Println("Starting basic HTTP server...")
	
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "Hello, World! Server is running.")
	})
	
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON>().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"status": "healthy", "message": "Basic server is running"}`)
	})
	
	port := "8080"
	fmt.Printf("Server starting on port %s\n", port)
	fmt.Printf("Visit http://localhost:%s/health to test\n", port)
	
	if err := http.ListenAndServe(":"+port, nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}
