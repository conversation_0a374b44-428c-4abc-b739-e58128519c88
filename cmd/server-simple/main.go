package main

import (
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"go-api-solve/internal/config"
	"go-api-solve/internal/middleware"
	"go-api-solve/pkg/database"
	"go-api-solve/pkg/redis"

	"github.com/gin-gonic/gin"
)

func main() {
	log.Println("Starting Go API Solve server...")
	
	// 加载配置
	cfg := config.LoadConfig()
	log.Printf("Config loaded: Server port %s, DB host %s:%s", cfg.Server.Port, cfg.Database.Host, cfg.Database.Port)
	
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Printf("Gin mode set to: %s", cfg.Server.Mode)

	// 初始化数据库连接
	log.Println("Initializing database connection...")
	if err := database.InitDB(cfg); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()
	log.Println("Database connected successfully")

	// 自动迁移数据库表
	log.Println("Running database migrations...")
	if err := database.AutoMigrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}
	log.Println("Database migrations completed")

	// 初始化Redis连接
	log.Println("Initializing Redis connection...")
	if err := redis.InitRedis(cfg); err != nil {
		log.Fatalf("Failed to initialize Redis: %v", err)
	}
	defer redis.CloseRedis()
	log.Println("Redis connected successfully")

	// 创建Gin路由器
	router := gin.New()

	// 添加中间件
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.CORS())

	// 注册简化的路由
	setupSimpleRoutes(router)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Server.Port)
	
	// 优雅关闭
	go func() {
		if err := router.Run(":" + cfg.Server.Port); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	log.Println("Server started successfully, waiting for requests...")

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Server shutting down...")
}

// setupSimpleRoutes 设置简化的路由
func setupSimpleRoutes(router *gin.Engine) {
	// API版本分组
	v1 := router.Group("/api/v1")
	{
		// 健康检查
		v1.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "服务正常运行",
				"data": gin.H{
					"status":  "healthy",
					"service": "go-api-solve",
					"version": "1.0.0",
				},
			})
		})
		
		// 简化的处理接口（不调用AI）
		v1.POST("/process-image", func(c *gin.Context) {
			var req struct {
				ImageURL string `json:"image_url" binding:"required"`
			}
			
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"code":    400,
					"message": "请求参数错误: " + err.Error(),
				})
				return
			}

			// 模拟响应
			c.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "处理成功（简化版本）",
				"data": []gin.H{
					{
						"question_type": "多选题",
						"question_text": "这是一个模拟的题目（简化版本）",
						"options": gin.H{
							"A": "选项A",
							"B": "选项B",
							"C": "选项C",
							"D": "选项D",
						},
						"answer": gin.H{
							"A": "选项A",
							"B": "选项B",
						},
						"analysis":    "这是模拟的解析内容",
						"image_url":   req.ImageURL,
						"user_image":  "",
						"is_verified": "0",
					},
				},
			})
		})
	}

	// 根路径重定向到健康检查
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/api/v1/health")
	})
}
