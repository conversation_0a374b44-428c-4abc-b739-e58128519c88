package main

import (
	"fmt"
	"log"
	"strings"

	"go-api-solve/internal/config"
	"go-api-solve/pkg/database"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	fmt.Printf("Testing database connection with config:\n")
	fmt.Printf("Database Host: %s:%s\n", cfg.Database.Host, cfg.Database.Port)
	fmt.Printf("Database User: %s\n", cfg.Database.Username)
	fmt.Printf("Database Name: %s\n", cfg.Database.Database)
	fmt.Printf("DSN: %s\n", cfg.GetDSN())
	fmt.Println(strings.Repeat("=", 50))

	// 测试数据库连接
	fmt.Println("Testing database connection...")
	if err := database.InitDB(cfg); err != nil {
		log.Printf("❌ Database connection failed: %v", err)
		return
	}
	fmt.Println("✅ Database connection successful!")

	// 测试自动迁移
	fmt.Println("Testing database migration...")
	if err := database.AutoMigrate(); err != nil {
		log.Printf("❌ Database migration failed: %v", err)
		database.CloseDB()
		return
	}
	fmt.Println("✅ Database migration successful!")

	// 关闭连接
	database.CloseDB()
	fmt.Println("🎉 Database test completed successfully!")
}
