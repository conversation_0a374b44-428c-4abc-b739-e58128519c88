/*
 Navicat Premium Dump SQL

 Source Server         : docker_80
 Source Server Type    : MySQL
 Source Server Version : 80021 (8.0.21)
 Source Host           : ***********:3380
 Source Schema         : solve_api_go

 Target Server Type    : MySQL
 Target Server Version : 80021 (8.0.21)
 File Encoding         : 65001

 Date: 10/06/2025 12:32:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for quest_model_config
-- ----------------------------
DROP TABLE IF EXISTS `quest_model_config`;
CREATE TABLE `quest_model_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名称',
  `role_system` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'system角色的Content',
  `role_user` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'user角色的Content',
  `temperature` decimal(3,2) DEFAULT '0.00' COMMENT '温度参数',
  `top_p` decimal(3,2) DEFAULT '0.80' COMMENT 'TopP参数',
  `top_k` bigint DEFAULT '50' COMMENT 'TopK参数',
  `repetition_penalty` decimal(4,3) DEFAULT '1.000' COMMENT '重复惩罚',
  `presence_penalty` decimal(4,3) DEFAULT '1.500' COMMENT '存在惩罚',
  `response_format` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'json_object' COMMENT '返回格式',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `is_active` tinyint DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_name` (`model_name`) COMMENT '模型名称唯一索引',
  UNIQUE KEY `model_name` (`model_name`),
  UNIQUE KEY `idx_quest_model_config_model_name` (`model_name`),
  KEY `idx_quest_model_config_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型配置表';

-- ----------------------------
-- Records of quest_model_config
-- ----------------------------
BEGIN;
INSERT INTO `quest_model_config` (`id`, `model_name`, `role_system`, `role_user`, `temperature`, `top_p`, `top_k`, `repetition_penalty`, `presence_penalty`, `response_format`, `created_at`, `updated_at`, `is_active`) VALUES (1, 'qwen-vl-plus', '精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\":\"单选题or多选题or判断题\",\"question_num\":\"问题的序号\",\"question_text\":\"完整的问题\",\"options\":{\"A\":\"选项内容\",\"B\":\"选项内容\",\"C\":\"选项内容\",\"D\":\"选项内容\",}}，', '返回的json中不应该有换行符或空格等', 0.00, 0.01, 1, 1.050, 1.500, 'json_object', '2025-06-09 09:36:21.000', '2025-06-09 09:36:21.000', 1);
INSERT INTO `quest_model_config` (`id`, `model_name`, `role_system`, `role_user`, `temperature`, `top_p`, `top_k`, `repetition_penalty`, `presence_penalty`, `response_format`, `created_at`, `updated_at`, `is_active`) VALUES (2, 'deepseek-chat', '你是一个专业的驾照科目考试专家，具有丰富的交通法规知识和考试经验。请根据提供的题目内容，给出准确的答案和详细的解析。', '严格标准返回json格式。{\"answer\":{\"A\":\"选项内容\",\"B\":\"选项内容\"},\"analysis\":\"解析内容\"}', 0.00, 0.01, 1, 0.000, 0.000, 'json_object', '2025-06-09 09:36:21.000', '2025-06-09 09:36:21.000', 1);
COMMIT;

-- ----------------------------
-- Table structure for questions
-- ----------------------------
DROP TABLE IF EXISTS `questions`;
CREATE TABLE `questions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cache_key_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被哈希化的缓存键名字',
  `question_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '问题类型',
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '问题内容',
  `option_a` text COLLATE utf8mb4_unicode_ci COMMENT '问题选项A',
  `option_b` text COLLATE utf8mb4_unicode_ci COMMENT '问题选项B',
  `option_c` text COLLATE utf8mb4_unicode_ci COMMENT '问题选项C',
  `option_d` text COLLATE utf8mb4_unicode_ci COMMENT '问题选项D',
  `option_y` text COLLATE utf8mb4_unicode_ci COMMENT '问题选项Y',
  `option_n` text COLLATE utf8mb4_unicode_ci COMMENT '问题选项N',
  `answer` json DEFAULT NULL COMMENT '问题答案',
  `analysis` text COLLATE utf8mb4_unicode_ci COMMENT '问题解析',
  `user_image` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '问题对应的图片名称',
  `image_url` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户提交的图片URL地址',
  `qwen_raw` json DEFAULT NULL COMMENT 'Qwen返回的原始数据',
  `deepseek_raw` json DEFAULT NULL COMMENT 'DeepSeek返回的原始数据',
  `qwen_parsed` json DEFAULT NULL COMMENT '被格式化解析后的Qwen数据',
  `is_verified` tinyint DEFAULT '0' COMMENT '是否已经验证过',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cache_key_hash` (`cache_key_hash`) COMMENT '缓存键哈希索引',
  KEY `idx_question_type` (`question_type`) COMMENT '问题类型索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引',
  KEY `idx_questions_cache_key_hash` (`cache_key_hash`),
  KEY `idx_questions_question_type` (`question_type`)
) ENGINE=InnoDB AUTO_INCREMENT=793 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目数据表';

-- ----------------------------
-- Records of questions
-- ----------------------------
BEGIN;
INSERT INTO `questions` (`id`, `cache_key_hash`, `question_type`, `question_text`, `option_a`, `option_b`, `option_c`, `option_d`, `option_y`, `option_n`, `answer`, `analysis`, `user_image`, `image_url`, `qwen_raw`, `deepseek_raw`, `qwen_parsed`, `is_verified`, `created_at`, `updated_at`) VALUES (788, '32b99e69667ea5a9d21a05f529f287a61a7156a8971830d76d9d409d8eebf503', '单选题', '如图所示，驾车遇到此情况时应当注意什么？', '左侧A柱盲区内可能有行人将要通过', '对向车道车辆将要调头', '后面有车辆将超车', '右侧车道有车辆将要通过', NULL, NULL, '{\"A\": \"左侧A柱盲区内可能有行人将要通过\"}', '在驾驶过程中，A柱盲区是驾驶员视线被车辆结构遮挡的区域，特别是在左转或通过交叉路口时，左侧A柱可能会遮挡行人或非机动车的视线。因此，驾车遇到此情况时应当特别注意左侧A柱盲区内可能有行人将要通过，以确保行车安全。其他选项虽然也是驾驶中需要注意的情况，但与题目描述的特定情境关联不大。', '01.jpg', 'http://solve.igmdns.com/img/01.jpg', '{\"usage\": {\"input_tokens\": 957, \"total_tokens\": 1062, \"output_tokens\": 105}, \"output\": {\"choices\": [{\"message\": {\"role\": \"assistant\", \"content\": [{\"text\": \"{\\n  \\\"question_type\\\": \\\"单选题\\\",\\n  \\\"question_num\\\": \\\"14\\\",\\n  \\\"question_text\\\": \\\"如图所示，驾车遇到此情况时应当注意什么？\\\",\\n  \\\"options\\\": {\\n    \\\"A\\\": \\\"左侧A柱盲区内可能有行人将要通过\\\",\\n    \\\"B\\\": \\\"对向车道车辆将要调头\\\",\\n    \\\"C\\\": \\\"后面有车辆将超车\\\",\\n    \\\"D\\\": \\\"右侧车道有车辆将要通过\\\"\\n  }\\n}\\n\"}]}, \"finish_reason\": \"stop\"}]}}', '{\"id\": \"886bfe84-e3c9-4522-8b36-00121ffaf1d7\", \"model\": \"deepseek-chat\", \"usage\": {\"total_tokens\": 238, \"prompt_tokens\": 137, \"completion_tokens\": 101}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\\"answer\\\":{\\\"A\\\":\\\"左侧A柱盲区内可能有行人将要通过\\\"},\\\"analysis\\\":\\\"在驾驶过程中，A柱盲区是驾驶员视线被车辆结构遮挡的区域，特别是在左转或通过交叉路口时，左侧A柱可能会遮挡行人或非机动车的视线。因此，驾车遇到此情况时应当特别注意左侧A柱盲区内可能有行人将要通过，以确保行车安全。其他选项虽然也是驾驶中需要注意的情况，但与题目描述的特定情境关联不大。\\\"}\"}, \"finish_reason\": \"stop\"}], \"created\": 1749527870}', '{\"options\": {\"A\": \"左侧A柱盲区内可能有行人将要通过\", \"B\": \"对向车道车辆将要调头\", \"C\": \"后面有车辆将超车\", \"D\": \"右侧车道有车辆将要通过\"}, \"question_num\": \"14\", \"question_text\": \"如图所示，驾车遇到此情况时应当注意什么？\", \"question_type\": \"单选题\"}', 0, '2025-06-10 11:57:58.488', '2025-06-10 11:57:58.488');
INSERT INTO `questions` (`id`, `cache_key_hash`, `question_type`, `question_text`, `option_a`, `option_b`, `option_c`, `option_d`, `option_y`, `option_n`, `answer`, `analysis`, `user_image`, `image_url`, `qwen_raw`, `deepseek_raw`, `qwen_parsed`, `is_verified`, `created_at`, `updated_at`) VALUES (789, '32b99e69667ea5a9d21a05f529f287a61a7156a8971830d76d9d409d8eebf503', '单选题', '如图所示，驾车遇到此情况时应当注意什么？', '左侧A柱盲区内可能有行人将要通过', '对向车道车辆将要调头', '后面有车辆将超车', '右侧车道有车辆将要通过', NULL, NULL, '{\"A\": \"左侧A柱盲区内可能有行人将要通过\"}', '在驾驶过程中，A柱盲区是驾驶员视线被车辆结构遮挡的区域，特别是在左转或通过交叉路口时，左侧A柱盲区可能会遮挡行人或非机动车的视线。因此，驾车遇到此情况时应当特别注意左侧A柱盲区内可能有行人将要通过，以确保行车安全。其他选项虽然也是驾驶中需要注意的情况，但与题目描述的特定情境关联不大。', '01.jpg', 'http://solve.igmdns.com/img/01.jpg', '{\"usage\": {\"input_tokens\": 957, \"total_tokens\": 1062, \"output_tokens\": 105}, \"output\": {\"choices\": [{\"message\": {\"role\": \"assistant\", \"content\": [{\"text\": \"{\\n  \\\"question_type\\\": \\\"单选题\\\",\\n  \\\"question_num\\\": \\\"14\\\",\\n  \\\"question_text\\\": \\\"如图所示，驾车遇到此情况时应当注意什么？\\\",\\n  \\\"options\\\": {\\n    \\\"A\\\": \\\"左侧A柱盲区内可能有行人将要通过\\\",\\n    \\\"B\\\": \\\"对向车道车辆将要调头\\\",\\n    \\\"C\\\": \\\"后面有车辆将超车\\\",\\n    \\\"D\\\": \\\"右侧车道有车辆将要通过\\\"\\n  }\\n}\\n\"}]}, \"finish_reason\": \"stop\"}]}}', '{\"id\": \"6f5c4f9e-c4a9-4595-9bd3-fbcd8a0e4d7d\", \"model\": \"deepseek-chat\", \"usage\": {\"total_tokens\": 240, \"prompt_tokens\": 137, \"completion_tokens\": 103}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\\"answer\\\":{\\\"A\\\":\\\"左侧A柱盲区内可能有行人将要通过\\\"},\\\"analysis\\\":\\\"在驾驶过程中，A柱盲区是驾驶员视线被车辆结构遮挡的区域，特别是在左转或通过交叉路口时，左侧A柱盲区可能会遮挡行人或非机动车的视线。因此，驾车遇到此情况时应当特别注意左侧A柱盲区内可能有行人将要通过，以确保行车安全。其他选项虽然也是驾驶中需要注意的情况，但与题目描述的特定情境关联不大。\\\"}\"}, \"finish_reason\": \"stop\"}], \"created\": 1749527875}', '{\"options\": {\"A\": \"左侧A柱盲区内可能有行人将要通过\", \"B\": \"对向车道车辆将要调头\", \"C\": \"后面有车辆将超车\", \"D\": \"右侧车道有车辆将要通过\"}, \"question_num\": \"14\", \"question_text\": \"如图所示，驾车遇到此情况时应当注意什么？\", \"question_type\": \"单选题\"}', 0, '2025-06-10 11:58:03.577', '2025-06-10 11:58:03.577');
INSERT INTO `questions` (`id`, `cache_key_hash`, `question_type`, `question_text`, `option_a`, `option_b`, `option_c`, `option_d`, `option_y`, `option_n`, `answer`, `analysis`, `user_image`, `image_url`, `qwen_raw`, `deepseek_raw`, `qwen_parsed`, `is_verified`, `created_at`, `updated_at`) VALUES (790, '1180173883280ccaf1bac78ea6dddeca4caf9490fe2cc88b65eb06117535d5f7', '单选题', '分心驾驶危险性很高，当你行车速度为60km/h时，驾驶人分心一秒，相当于你驾车盲行了多远的距离？', '17米', '27米', '7米', '14米', NULL, NULL, '{\"A\": \"17米\"}', '分心驾驶时，驾驶人的注意力不在驾驶上，导致车辆在无人控制的状态下行驶。当行车速度为60km/h时，换算成米每秒约为16.67米/秒（60km/h = 60,000米/3,600秒 ≈ 16.67米/秒）。因此，分心一秒相当于车辆盲行了约16.67米，四舍五入后最接近的选项是17米。', '02.jpg', 'http://solve.igmdns.com/img/02.jpg', '{\"usage\": {\"input_tokens\": 957, \"total_tokens\": 1060, \"output_tokens\": 103}, \"output\": {\"choices\": [{\"message\": {\"role\": \"assistant\", \"content\": [{\"text\": \"{\\n  \\\"question_type\\\": \\\"单选题\\\",\\n  \\\"question_num\\\": \\\"2\\\",\\n  \\\"question_text\\\": \\\"分心驾驶危险性很高，当你行车速度为60km/h时，驾驶人分心一秒，相当于你驾车盲行了多远的距离？\\\",\\n  \\\"options\\\": {\\n    \\\"A\\\": \\\"17米\\\",\\n    \\\"B\\\": \\\"27米\\\",\\n    \\\"C\\\": \\\"7米\\\",\\n    \\\"D\\\": \\\"14米\\\"\\n  }\\n}\\n\"}]}, \"finish_reason\": \"stop\"}]}}', '{\"id\": \"846f02f5-8666-4318-b12c-58ab4b0f2477\", \"model\": \"deepseek-chat\", \"usage\": {\"total_tokens\": 242, \"prompt_tokens\": 134, \"completion_tokens\": 108}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\\"answer\\\":{\\\"A\\\":\\\"17米\\\"},\\\"analysis\\\":\\\"分心驾驶时，驾驶人的注意力不在驾驶上，导致车辆在无人控制的状态下行驶。当行车速度为60km/h时，换算成米每秒约为16.67米/秒（60km/h = 60,000米/3,600秒 ≈ 16.67米/秒）。因此，分心一秒相当于车辆盲行了约16.67米，四舍五入后最接近的选项是17米。\\\"}\"}, \"finish_reason\": \"stop\"}], \"created\": 1749527939}', '{\"options\": {\"A\": \"17米\", \"B\": \"27米\", \"C\": \"7米\", \"D\": \"14米\"}, \"question_num\": \"2\", \"question_text\": \"分心驾驶危险性很高，当你行车速度为60km/h时，驾驶人分心一秒，相当于你驾车盲行了多远的距离？\", \"question_type\": \"单选题\"}', 0, '2025-06-10 11:59:05.957', '2025-06-10 11:59:05.957');
INSERT INTO `questions` (`id`, `cache_key_hash`, `question_type`, `question_text`, `option_a`, `option_b`, `option_c`, `option_d`, `option_y`, `option_n`, `answer`, `analysis`, `user_image`, `image_url`, `qwen_raw`, `deepseek_raw`, `qwen_parsed`, `is_verified`, `created_at`, `updated_at`) VALUES (791, 'b5e9f818f77e76bd454f11588187583910514478cea2c26a36b258c88aac2593', '多选题', '驾驶机动车频繁变更车道有哪些危害？', '扰乱交通秩序', '易引发交通事故', '影响正常通行', '易导致爆胎', NULL, NULL, '{\"A\": \"扰乱交通秩序\", \"B\": \"易引发交通事故\", \"C\": \"影响正常通行\"}', '驾驶机动车频繁变更车道会扰乱交通秩序，因为这种行为会让其他驾驶员难以预测你的行驶路线，从而增加交通事故的风险。同时，频繁变更车道也会影响道路上其他车辆的正常通行，导致交通效率下降。选项D\'易导致爆胎\'与频繁变更车道无直接关联，因此不选。', '04.jpg', 'http://solve.igmdns.com/img/04.jpg', '{\"usage\": {\"input_tokens\": 957, \"total_tokens\": 1038, \"output_tokens\": 81}, \"output\": {\"choices\": [{\"message\": {\"role\": \"assistant\", \"content\": [{\"text\": \"{\\n  \\\"question_type\\\": \\\"多选题\\\",\\n  \\\"question_num\\\": \\\"18\\\",\\n  \\\"question_text\\\": \\\"驾驶机动车频繁变更车道有哪些危害？\\\",\\n  \\\"options\\\": {\\n    \\\"A\\\": \\\"扰乱交通秩序\\\",\\n    \\\"B\\\": \\\"易引发交通事故\\\",\\n    \\\"C\\\": \\\"影响正常通行\\\",\\n    \\\"D\\\": \\\"易导致爆胎\\\"\\n  }\\n}\\n\"}]}, \"finish_reason\": \"stop\"}]}}', '{\"id\": \"d53ce670-49df-44f5-9539-7f8d454713e1\", \"model\": \"deepseek-chat\", \"usage\": {\"total_tokens\": 229, \"prompt_tokens\": 119, \"completion_tokens\": 110}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\n  \\\"answer\\\": {\\n    \\\"A\\\": \\\"扰乱交通秩序\\\",\\n    \\\"B\\\": \\\"易引发交通事故\\\",\\n    \\\"C\\\": \\\"影响正常通行\\\"\\n  },\\n  \\\"analysis\\\": \\\"驾驶机动车频繁变更车道会扰乱交通秩序，因为这种行为会让其他驾驶员难以预测你的行驶路线，从而增加交通事故的风险。同时，频繁变更车道也会影响道路上其他车辆的正常通行，导致交通效率下降。选项D\'易导致爆胎\'与频繁变更车道无直接关联，因此不选。\\\"\\n}\"}, \"finish_reason\": \"stop\"}], \"created\": 1749529567}', '{\"options\": {\"A\": \"扰乱交通秩序\", \"B\": \"易引发交通事故\", \"C\": \"影响正常通行\", \"D\": \"易导致爆胎\"}, \"question_num\": \"18\", \"question_text\": \"驾驶机动车频繁变更车道有哪些危害？\", \"question_type\": \"多选题\"}', 0, '2025-06-10 12:26:14.612', '2025-06-10 12:26:14.612');
INSERT INTO `questions` (`id`, `cache_key_hash`, `question_type`, `question_text`, `option_a`, `option_b`, `option_c`, `option_d`, `option_y`, `option_n`, `answer`, `analysis`, `user_image`, `image_url`, `qwen_raw`, `deepseek_raw`, `qwen_parsed`, `is_verified`, `created_at`, `updated_at`) VALUES (792, '400d2f3c4cabf044a312d26f4e12b1ccb5dc45d7c793dc1420ea71b6ecabe1e8', '判断题', '如图所示，此时绿灯亮起，车辆可以进入路面网状线区域内等待通行。', NULL, NULL, NULL, NULL, '正确', '错误', '{\"N\": \"错误\"}', '根据交通法规，绿灯亮起时，车辆可以通行，但不得进入路面网状线区域内等待。网状线区域用于标示禁止以任何原因停车的区域，确保交叉路口的畅通。因此，即使绿灯亮起，车辆也不应在网状线区域内等待通行，以避免阻碍其他方向的交通流。', '08.jpg', 'http://solve.igmdns.com/img/08.jpg', '{\"usage\": {\"input_tokens\": 801, \"total_tokens\": 880, \"output_tokens\": 79}, \"output\": {\"choices\": [{\"message\": {\"role\": \"assistant\", \"content\": [{\"text\": \"{\\n  \\\"question_type\\\": \\\"判断题\\\",\\n  \\\"question_num\\\": \\\"19\\\",\\n  \\\"question_text\\\": \\\"(判断题)19、如图所示，此时绿灯亮起，车辆可以进入路面网状线区域内等待通行。\\\",\\n  \\\"options\\\": {\\n    \\\"Y: 正确\\\": \\\"\\\",\\n    \\\"N: 错误\\\": \\\"\\\"\\n  }\\n}\\n\"}]}, \"finish_reason\": \"stop\"}]}}', '{\"id\": \"dba650f9-44f4-4679-83fd-d03a7e6f01fc\", \"model\": \"deepseek-chat\", \"usage\": {\"total_tokens\": 190, \"prompt_tokens\": 111, \"completion_tokens\": 79}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\\"answer\\\":{\\\"N\\\":\\\"错误\\\"},\\\"analysis\\\":\\\"根据交通法规，绿灯亮起时，车辆可以通行，但不得进入路面网状线区域内等待。网状线区域用于标示禁止以任何原因停车的区域，确保交叉路口的畅通。因此，即使绿灯亮起，车辆也不应在网状线区域内等待通行，以避免阻碍其他方向的交通流。\\\"}\"}, \"finish_reason\": \"stop\"}], \"created\": 1749529603}', '{\"options\": {\"N\": \"错误\", \"Y\": \"正确\"}, \"question_num\": \"19\", \"question_text\": \"如图所示，此时绿灯亮起，车辆可以进入路面网状线区域内等待通行。\", \"question_type\": \"判断题\"}', 0, '2025-06-10 12:26:49.321', '2025-06-10 12:26:49.321');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
