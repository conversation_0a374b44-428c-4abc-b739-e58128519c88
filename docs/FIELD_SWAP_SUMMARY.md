# user_image 和 image_url 字段业务用途交换修复

## 🔍 修改概述

根据业务需求，交换了 `user_image` 和 `image_url` 两个字段的业务用途。

### 修改前的字段用途：
- `user_image` = 问题对应的图片名称（由管理员后期手动添加）
- `image_url` = 用户提交的图片 URL 地址（API自动填充）

### 修改后的字段用途：
- `user_image` = 用户提交的图片 URL 地址（API自动填充）
- `image_url` = 问题对应的图片名称（由管理员后期手动添加）

## 📝 修改文件清单

### 1. 数据库模型修改
**文件**: `internal/model/question.go`
- 交换了 `UserImage` 和 `ImageURL` 字段的类型和注释
- `UserImage` 改为 `string` 类型，`not null`
- `ImageURL` 改为 `*string` 类型，`omitempty`

### 2. 业务逻辑修改
**文件**: `internal/utils/format.go`
- `ConvertQwenToQuestion` 函数：将 `imageURL` 参数赋值给 `UserImage` 字段
- `ConvertToQuestionResponse` 函数：交换了字段的处理逻辑

**文件**: `internal/service/question.go`
- 更新了注释：`image_url字段保持为空，由管理员后期手动添加`

### 3. 文档更新
**文件**: `SaveDeepseekToDatabase.md`
- 更新字段描述
- 更新所有示例中的字段值

**文件**: `s1.md`
- 更新字段描述

**文件**: `docs/USER_IMAGE_FIELD_FIX.md`
- 重写文档内容以反映新的字段用途

## 🎯 业务影响

### 数据库结构变化
- `user_image` 字段现在是必填字段，存储用户提交的图片URL
- `image_url` 字段现在是可选字段，由管理员手动填充

### API响应格式变化
```json
{
  "user_image": "http://example.com/user-uploaded-image.jpg",
  "image_url": ""
}
```

### 注意事项
⚠️ **重要**: 这个修改需要数据库迁移脚本来交换现有数据中这两个字段的值。

## ✅ 验证结果

- ✅ 代码编译无错误
- ✅ 字段类型和注释已正确交换
- ✅ 业务逻辑已更新
- ✅ 文档已同步更新
- ⚠️ 需要数据迁移脚本处理现有数据

## 📋 后续工作

1. **数据迁移**: 需要编写SQL脚本交换现有数据中的字段值
2. **测试验证**: 需要测试新的字段逻辑是否正常工作
3. **管理后台**: 确保管理员可以编辑 `image_url` 字段

---

**修改完成时间**: 2024年12月
**修改人员**: Augment Agent
**业务确认**: ✅ 已确认
