# 部署指南

## 环境要求

### 系统要求
- Linux/macOS/Windows
- Go 1.21 或更高版本
- MySQL 8.0 或更高版本
- Redis 6.0 或更高版本

### 网络要求
- 能够访问外部AI API（Qwen和DeepSeek）
- 能够访问用户提供的图片URL

## 部署步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd Go_api_solve
```

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 配置数据库

#### 3.1 创建MySQL数据库
```sql
CREATE DATABASE solve_api_go CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 3.2 运行数据库迁移
```bash
# 方式1: 使用SQL文件手动执行
mysql -h *********** -P 3306 -u gmdns -p solve_api_go < migrations/001_create_questions_table.sql
mysql -h *********** -P 3306 -u gmdns -p solve_api_go < migrations/002_create_model_config_table.sql

# 方式2: 程序启动时自动迁移（推荐）
# 程序会在启动时自动执行GORM的AutoMigrate
```

### 4. 配置环境变量

#### 4.1 创建环境变量文件
```bash
# 创建 .env 文件（可选）
cat > .env << EOF
# 服务器配置
SERVER_PORT=8080
GIN_MODE=release

# 数据库配置
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az
MYSQL_DATABASE=solve_api_go

# Redis配置
REDIS_HOST=r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_USERNAME=r-bp1t323p6w8yn2cpq0
REDIS_PASSWORD=SsYZyxSSr8uEVWKJ

# AI模型配置
QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84
DEEPSEEK_KEY=***********************************
EOF
```

#### 4.2 加载环境变量
```bash
# 如果使用.env文件
export $(cat .env | xargs)

# 或者直接设置
export MYSQL_HOST=***********
export MYSQL_PORT=3306
# ... 其他变量
```

### 5. 编译和运行

#### 5.1 开发模式运行
```bash
go run cmd/server/main.go
```

#### 5.2 生产模式部署
```bash
# 编译
go build -o bin/server cmd/server/main.go

# 运行
./bin/server
```

### 6. 验证部署

#### 6.1 健康检查
```bash
curl http://localhost:8080/api/v1/health
```

#### 6.2 功能测试
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}'
```

## 生产环境配置

### 1. 使用systemd管理服务

#### 1.1 创建服务文件
```bash
sudo tee /etc/systemd/system/go-api-solve.service << EOF
[Unit]
Description=Go API Solve Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/go-api-solve
ExecStart=/opt/go-api-solve/bin/server
Restart=always
RestartSec=5

# 环境变量
Environment=GIN_MODE=release
Environment=MYSQL_HOST=***********
Environment=MYSQL_PORT=3306
Environment=MYSQL_USERNAME=gmdns
Environment=MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az
Environment=MYSQL_DATABASE=solve_api_go
Environment=REDIS_HOST=r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com
Environment=REDIS_PORT=6379
Environment=REDIS_USERNAME=r-bp1t323p6w8yn2cpq0
Environment=REDIS_PASSWORD=SsYZyxSSr8uEVWKJ
Environment=QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84
Environment=DEEPSEEK_KEY=***********************************

[Install]
WantedBy=multi-user.target
EOF
```

#### 1.2 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable go-api-solve
sudo systemctl start go-api-solve
sudo systemctl status go-api-solve
```

### 2. 使用Nginx反向代理

#### 2.1 Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 3. 使用Docker部署

#### 3.1 创建Dockerfile
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o bin/server cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/bin/server .
COPY --from=builder /app/migrations ./migrations

CMD ["./server"]
```

#### 3.2 构建和运行
```bash
# 构建镜像
docker build -t go-api-solve .

# 运行容器
docker run -d \
  --name go-api-solve \
  -p 8080:8080 \
  -e MYSQL_HOST=*********** \
  -e MYSQL_PORT=3306 \
  -e MYSQL_USERNAME=gmdns \
  -e MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az \
  -e MYSQL_DATABASE=solve_api_go \
  -e REDIS_HOST=r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com \
  -e REDIS_PORT=6379 \
  -e REDIS_USERNAME=r-bp1t323p6w8yn2cpq0 \
  -e REDIS_PASSWORD=SsYZyxSSr8uEVWKJ \
  -e QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84 \
  -e DEEPSEEK_KEY=*********************************** \
  go-api-solve
```

## 监控和日志

### 1. 日志管理
```bash
# 查看服务日志
sudo journalctl -u go-api-solve -f

# 或者使用Docker
docker logs -f go-api-solve
```

### 2. 性能监控
- 监控CPU和内存使用率
- 监控数据库连接数
- 监控Redis连接状态
- 监控API响应时间

## 故障排除

### 1. 常见问题

#### 数据库连接失败
```bash
# 检查数据库连接
mysql -h *********** -P 3306 -u gmdns -p
```

#### Redis连接失败
```bash
# 检查Redis连接
redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -p 6379 -a SsYZyxSSr8uEVWKJ
```

#### AI API调用失败
- 检查API密钥是否正确
- 检查网络连接
- 检查API配额

### 2. 调试模式
```bash
# 开启调试模式
export GIN_MODE=debug
go run cmd/server/main.go
```
