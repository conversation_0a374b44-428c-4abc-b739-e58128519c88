# API 测试文档

## 测试环境准备

### 1. 启动服务
```bash
go run cmd/server/main.go
```

### 2. 验证服务状态
```bash
curl http://localhost:8080/api/v1/health
```

预期响应：
```json
{
  "code": 200,
  "message": "服务正常运行",
  "data": {
    "status": "healthy",
    "service": "go-api-solve"
  }
}
```

## API 接口测试

### 1. 处理图片题目接口

#### 1.1 基本测试
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}'
```

#### 1.2 测试用例

##### 测试用例1：正常图片URL
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "http://solve.igmdns.com/img/24.jpg"
  }'
```

预期响应：
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "多选题",
      "question_text": "雾天跟车行驶,应如何安全驾驶?",
      "options": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物",
        "D": "按喇叭提示行车位置"
      },
      "answer": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物"
      },
      "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度...",
      "image_url": "http://solve.igmdns.com/img/24.jpg",
      "user_image": "",
      "is_verified": "0"
    }
  ]
}
```

##### 测试用例2：无效图片URL
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "http://invalid-url.com/nonexistent.jpg"
  }'
```

预期响应：
```json
{
  "code": 400,
  "message": "图片资源不存在，请重新上传"
}
```

##### 测试用例3：空图片URL
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": ""
  }'
```

预期响应：
```json
{
  "code": 400,
  "message": "图片URL不能为空"
}
```

##### 测试用例4：缺少参数
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{}'
```

预期响应：
```json
{
  "code": 400,
  "message": "请求参数错误: Key: 'ProcessImageRequest.ImageURL' Error:Field validation for 'ImageURL' failed on the 'required' tag"
}
```

##### 测试用例5：判断题
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "http://example.com/judgment-question.jpg"
  }'
```

预期响应（判断题格式）：
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "判断题",
      "question_text": "驾驶机动车在雾天应使用远光灯提高能见度。",
      "options": {
        "Y": "正确",
        "N": "错误"
      },
      "answer": {
        "N": "错误"
      },
      "analysis": "雾天使用远光灯会导致光线反射，降低能见度，正确做法是使用雾灯，因此本题错误。",
      "image_url": "http://example.com/judgment-question.jpg",
      "user_image": "",
      "is_verified": "0"
    }
  ]
}
```

## 性能测试

### 1. 并发测试
使用Apache Bench进行并发测试：

```bash
# 安装ab工具
sudo apt-get install apache2-utils  # Ubuntu/Debian
brew install httpie                  # macOS

# 并发测试
ab -n 100 -c 10 -p test_data.json -T application/json http://localhost:8080/api/v1/process-image
```

test_data.json内容：
```json
{"image_url": "http://solve.igmdns.com/img/24.jpg"}
```

### 2. 缓存测试
```bash
# 第一次请求（应该调用AI模型）
time curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}'

# 第二次请求（应该从缓存返回，速度更快）
time curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}'
```

## 错误处理测试

### 1. 数据库连接错误
停止MySQL服务，测试API响应：
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}'
```

### 2. Redis连接错误
停止Redis服务，测试API响应：
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}'
```

### 3. AI API错误
使用无效的API密钥，测试错误处理。

## 自动化测试脚本

### 1. 基本功能测试脚本
```bash
#!/bin/bash

# test_api.sh
BASE_URL="http://localhost:8080/api/v1"

echo "=== API 自动化测试 ==="

# 测试健康检查
echo "1. 测试健康检查..."
curl -s "$BASE_URL/health" | jq .

# 测试正常图片处理
echo "2. 测试正常图片处理..."
curl -s -X POST "$BASE_URL/process-image" \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}' | jq .

# 测试无效URL
echo "3. 测试无效URL..."
curl -s -X POST "$BASE_URL/process-image" \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://invalid-url.com/test.jpg"}' | jq .

# 测试空参数
echo "4. 测试空参数..."
curl -s -X POST "$BASE_URL/process-image" \
  -H "Content-Type: application/json" \
  -d '{}' | jq .

echo "=== 测试完成 ==="
```

### 2. 运行测试脚本
```bash
chmod +x test_api.sh
./test_api.sh
```

## 监控和日志

### 1. 查看应用日志
```bash
# 如果使用systemd
sudo journalctl -u go-api-solve -f

# 如果直接运行
# 日志会输出到控制台
```

### 2. 监控关键指标
- API响应时间
- 数据库连接数
- Redis连接状态
- 内存使用情况
- CPU使用率

### 3. 错误日志分析
查看日志中的错误信息，常见错误类型：
- 图片URL访问失败
- AI API调用超时
- 数据库连接错误
- Redis连接错误
- JSON解析错误

## 测试数据

### 测试图片URL列表
```
http://solve.igmdns.com/img/24.jpg
http://solve.igmdns.com/img/25.jpg
http://solve.igmdns.com/img/26.jpg
```

### 预期的题目类型
- 单选题
- 多选题  
- 判断题

每种类型都应该能正确识别和处理。
