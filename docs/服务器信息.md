#正式服务器配置信息
host：**************
name：Solve-Go-Api服务器
password：solve-go-api.pem
```
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************```

宝塔信息：
面板地址: https://**************:21240/87250552
username: coulson
password: Suyan15913..
Google Authenticator: true

正式业务域名：http://solve.igmdns.com/  映射到   8080端口

```
SERVER_PORT=8080

GIN_MODE=release

MYSQL_HOST=rm-bp1a4xjg3ec33vn13.mysql.rds.aliyuncs.com
MYSQL_PORT=3306
MYSQL_USERNAME=solve_user
MYSQL_PASSWORD=jzS5U38yDahH6Jcm
MYSQL_DATABASE=solve
MYSQL_CHARSET=utf8mb4

REDIS_HOST=solve-go-api.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_USERNAME=r-bp1t323p6w8yn2cpq0
REDIS_PASSWORD=EtdDj8xJ385pUPUT
REDIS_DB=0


QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84
DEEPSEEK_KEY=***********************************

```






# 测试服务器配置


#正式服务器配置信息
host：***********
name：Docker_MySQL_Redis
password：Docker_MySQL_Redis.pem
```
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************```

宝塔信息：
面板地址: https://***********:16674/e89903ad
username: coulson
password: Suyan15913..
Google Authenticator: true



```
SERVER_PORT=8080
GIN_MODE=release


MySQL_8数据库
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=t_solve_go_api
MYSQL_CHARSET=utf8mb4

REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..
REDIS_DB=0


QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84
DEEPSEEK_KEY=***********************************

MySQL_5.6数据库
MYSQL_HOST=***********
MYSQL_PORT=3356
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=
MYSQL_CHARSET=

```