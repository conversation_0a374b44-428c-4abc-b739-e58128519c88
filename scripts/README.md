# API 自动化测试脚本

这里提供了三种不同的API测试脚本，用于轮询测试您的图片解析API。每个脚本都会从 `http://solve.igmdns.com/img/01.jpg` 到 `http://solve.igmdns.com/img/200.jpg` 进行200次请求，每次间隔15秒。

## 📁 脚本文件

- `api_test.go` - Go语言版本（功能最完整）
- `api_test.py` - Python版本（推荐使用）
- `api_test.sh` - Shell脚本版本（轻量级）

## 🚀 使用方法

### 1. Python版本 (推荐)

**安装依赖:**
```bash
pip install requests
```

**基本使用:**
```bash
# 使用默认配置 (localhost:8080)
python3 scripts/api_test.py

# 指定API地址
python3 scripts/api_test.py --api-url http://your-server:8080

# 自定义参数
python3 scripts/api_test.py \
  --api-url http://your-server:8080 \
  --start 1 \
  --end 50 \
  --interval 10 \
  --timeout 30
```

**参数说明:**
- `--api-url`: API服务地址 (默认: http://localhost:8080)
- `--start`: 起始图片编号 (默认: 1)
- `--end`: 结束图片编号 (默认: 200)
- `--interval`: 请求间隔秒数 (默认: 15)
- `--timeout`: 请求超时秒数 (默认: 30)

### 2. Shell脚本版本

**安装依赖:**
```bash
# Ubuntu/Debian
sudo apt-get install curl bc

# CentOS/RHEL
sudo yum install curl bc

# macOS
brew install bc  # curl通常已安装
```

**使用方法:**
```bash
# 给脚本执行权限
chmod +x scripts/api_test.sh

# 使用默认配置
./scripts/api_test.sh

# 指定API地址
./scripts/api_test.sh http://your-server:8080
```

### 3. Go语言版本

**使用方法:**
```bash
# 编译并运行
cd scripts
go run api_test.go

# 或者编译后运行
go build -o api_test api_test.go
./api_test

# 设置环境变量指定API地址
export API_URL=http://your-server:8080
go run api_test.go
```

## 📊 测试结果

每次运行测试都会生成一个带时间戳的结果目录，例如 `test_results_20231210_143022/`，包含以下文件：

### 通用文件
- `api_test.log` - 完整的测试日志
- `success.log` - 成功请求的详细记录
- `errors.log` - 失败请求的错误信息

### Python版本额外文件
- `test_report.json` - 详细的JSON格式测试报告

### Shell版本额外文件
- `test_report.txt` - 文本格式的测试报告摘要

## 📈 测试报告示例

### 控制台输出示例
```
🚀 开始API测试
📍 API地址: http://localhost:8080/api/v1/process-image
🖼️  图片范围: 01.jpg - 200.jpg
⏱️  请求间隔: 15秒
⏰ 超时时间: 30秒
📊 总请求数: 200
🕐 预计耗时: 49.8分钟
========================================

[1/200] 测试图片: 01.jpg
✅ 成功 - 响应时间: 2.345秒
📊 当前统计: 成功 1, 失败 0, 超时 0
⏳ 等待 15秒后继续...

[2/200] 测试图片: 02.jpg
❌ 失败 - 错误: 图片资源不存在，请重新上传
📊 当前统计: 成功 1, 失败 1, 超时 0
...
```

### 最终报告示例
```
========================================
📋 测试报告摘要
========================================
🕐 测试时间: 2023-12-10 14:30:22 - 2023-12-10 15:20:15
⏱️  总耗时: 2993.2秒 (49.9分钟)
📊 总请求数: 200
✅ 成功请求: 185 (92.50%)
❌ 失败请求: 15 (7.50%)
⏰ 超时请求: 3 (1.50%)

📈 响应时间统计:
   平均响应时间: 2.456秒
   最快响应时间: 1.234秒
   最慢响应时间: 8.901秒
   中位数响应时间: 2.123秒
========================================
```

## 🔧 配置说明

### 默认配置
- **API地址**: `http://localhost:8080`
- **图片URL**: `http://solve.igmdns.com/img/{01-200}.jpg`
- **请求间隔**: 15秒
- **超时时间**: 30秒
- **测试范围**: 01.jpg - 200.jpg (共200张图片)

### 自定义配置
所有脚本都支持自定义配置，可以根据需要调整：

1. **API地址**: 修改为您的实际服务地址
2. **图片范围**: 可以测试部分图片，如01-50
3. **请求间隔**: 根据服务器负载调整
4. **超时时间**: 根据网络情况调整

## ⚠️ 注意事项

1. **服务器负载**: 200次请求可能对服务器造成一定负载，建议在测试环境先试运行
2. **网络稳定性**: 确保网络连接稳定，避免因网络问题导致误判
3. **图片资源**: 确保测试图片URL可访问
4. **磁盘空间**: 测试日志会占用一定磁盘空间
5. **测试时间**: 完整测试需要约50分钟，请确保有足够时间

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   - 检查API服务是否正常运行
   - 确认API地址和端口正确
   - 检查防火墙设置

2. **请求超时**
   - 增加超时时间参数
   - 检查网络连接
   - 确认服务器响应正常

3. **权限错误**
   - Shell脚本需要执行权限: `chmod +x api_test.sh`
   - 确保有写入测试结果目录的权限

4. **依赖缺失**
   - Python版本需要 `requests` 库
   - Shell版本需要 `curl` 和 `bc` 命令
   - Go版本需要Go环境

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. 测试日志文件中的错误信息
2. API服务的运行状态
3. 网络连接情况
4. 脚本依赖是否正确安装

建议优先使用Python版本，功能最完整且错误处理最好。
