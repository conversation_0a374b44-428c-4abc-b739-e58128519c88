#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API 自动化测试脚本
轮询测试 200 张图片，每 15 秒发起一次请求
"""

import requests
import json
import time
import os
import sys
from datetime import datetime
from typing import Dict, List, Tuple
import argparse
import statistics

class APITester:
    def __init__(self, api_url: str = "http://localhost:8080", 
                 image_base_url: str = "http://solve.igmdns.com/img/",
                 start_image: int = 1, end_image: int = 200,
                 interval: int = 15, timeout: int = 30):
        self.api_url = api_url
        self.image_base_url = image_base_url
        self.start_image = start_image
        self.end_image = end_image
        self.interval = interval
        self.timeout = timeout
        
        # 统计数据
        self.total_requests = 0
        self.success_requests = 0
        self.failed_requests = 0
        self.timeout_requests = 0
        self.response_times = []
        self.errors = []
        self.start_time = None
        self.end_time = None
        
        # 创建结果目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.result_dir = f"test_results_{self.timestamp}"
        os.makedirs(self.result_dir, exist_ok=True)
        
        # 设置日志文件
        self.log_file = os.path.join(self.result_dir, "api_test.log")
        self.success_file = os.path.join(self.result_dir, "success.log")
        self.error_file = os.path.join(self.result_dir, "errors.log")
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        print(log_message)
        
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(log_message + "\n")
    
    def send_request(self, image_num: int) -> Tuple[bool, float, str]:
        """发送API请求"""
        image_url = f"{self.image_base_url}{image_num:02d}.jpg"
        api_endpoint = f"{self.api_url}/api/v1/process-image"
        
        # 准备请求数据
        payload = {"image_url": image_url}
        headers = {"Content-Type": "application/json"}
        
        try:
            # 发送请求并计时
            start_time = time.time()
            response = requests.post(
                api_endpoint,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            response_time = time.time() - start_time
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return False, response_time, f"HTTP错误: {response.status_code}"
            
            # 解析JSON响应
            try:
                data = response.json()
                if data.get("code") == 200:
                    return True, response_time, "成功"
                else:
                    return False, response_time, f"API错误: {data.get('message', '未知错误')}"
            except json.JSONDecodeError:
                return False, response_time, "响应不是有效的JSON格式"
                
        except requests.exceptions.Timeout:
            return False, self.timeout, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, 0, "连接失败"
        except Exception as e:
            return False, 0, f"未知错误: {str(e)}"
    
    def run_test(self):
        """运行测试"""
        self.start_time = datetime.now()
        
        # 打印测试信息
        self.log("🚀 开始API测试")
        self.log(f"📍 API地址: {self.api_url}/api/v1/process-image")
        self.log(f"🖼️  图片范围: {self.start_image:02d}.jpg - {self.end_image:02d}.jpg")
        self.log(f"⏱️  请求间隔: {self.interval}秒")
        self.log(f"⏰ 超时时间: {self.timeout}秒")
        self.log(f"📊 总请求数: {self.end_image - self.start_image + 1}")
        estimated_time = (self.end_image - self.start_image) * self.interval / 60
        self.log(f"🕐 预计耗时: {estimated_time:.1f}分钟")
        self.log(f"📁 结果目录: {self.result_dir}")
        self.log("=" * 60)
        
        # 开始测试循环
        for i in range(self.start_image, self.end_image + 1):
            self.log(f"\n[{i}/{self.end_image}] 测试图片: {i:02d}.jpg")
            
            # 发送请求
            success, response_time, message = self.send_request(i)
            
            # 更新统计
            self.total_requests += 1
            self.response_times.append(response_time)
            
            if success:
                self.success_requests += 1
                self.log(f"✅ 成功 - 响应时间: {response_time:.3f}秒", "SUCCESS")
                
                # 记录成功日志
                with open(self.success_file, "a", encoding="utf-8") as f:
                    f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - "
                           f"图片 {i:02d}.jpg - 成功 - {response_time:.3f}秒\n")
            else:
                self.failed_requests += 1
                if "超时" in message:
                    self.timeout_requests += 1
                    self.log(f"⏰ 超时 - 响应时间: {response_time:.3f}秒", "TIMEOUT")
                else:
                    self.log(f"❌ 失败 - 错误: {message}", "ERROR")
                
                # 记录错误
                error_msg = f"图片 {i:02d}.jpg: {message}"
                self.errors.append(error_msg)
                
                # 记录错误日志
                with open(self.error_file, "a", encoding="utf-8") as f:
                    f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {error_msg}\n")
            
            # 显示当前统计
            self.log(f"📊 当前统计: 成功 {self.success_requests}, "
                    f"失败 {self.failed_requests}, 超时 {self.timeout_requests}")
            
            # 如果不是最后一个请求，等待间隔时间
            if i < self.end_image:
                self.log(f"⏳ 等待 {self.interval}秒后继续...")
                time.sleep(self.interval)
        
        # 测试完成
        self.end_time = datetime.now()
        self.log("\n" + "=" * 60)
        self.log("🎉 测试完成!")
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        self.log("📋 生成测试报告")
        
        # 计算统计数据
        total_time = (self.end_time - self.start_time).total_seconds()
        success_rate = (self.success_requests / self.total_requests * 100) if self.total_requests > 0 else 0
        
        # 响应时间统计
        if self.response_times:
            avg_response_time = statistics.mean(self.response_times)
            min_response_time = min(self.response_times)
            max_response_time = max(self.response_times)
            median_response_time = statistics.median(self.response_times)
        else:
            avg_response_time = min_response_time = max_response_time = median_response_time = 0
        
        # 生成报告内容
        report = {
            "test_config": {
                "api_url": self.api_url,
                "image_base_url": self.image_base_url,
                "start_image": self.start_image,
                "end_image": self.end_image,
                "interval": self.interval,
                "timeout": self.timeout
            },
            "test_results": {
                "start_time": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": self.end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_time_seconds": total_time,
                "total_requests": self.total_requests,
                "success_requests": self.success_requests,
                "failed_requests": self.failed_requests,
                "timeout_requests": self.timeout_requests,
                "success_rate": round(success_rate, 2)
            },
            "response_time_stats": {
                "average": round(avg_response_time, 3),
                "minimum": round(min_response_time, 3),
                "maximum": round(max_response_time, 3),
                "median": round(median_response_time, 3)
            },
            "errors": self.errors[:20]  # 只保存前20个错误
        }
        
        # 保存JSON报告
        report_file = os.path.join(self.result_dir, "test_report.json")
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印报告摘要
        self.log("=" * 60)
        self.log("📋 测试报告摘要")
        self.log("=" * 60)
        self.log(f"🕐 测试时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.log(f"⏱️  总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
        self.log(f"📊 总请求数: {self.total_requests}")
        self.log(f"✅ 成功请求: {self.success_requests} ({success_rate:.2f}%)")
        self.log(f"❌ 失败请求: {self.failed_requests} ({(self.failed_requests/self.total_requests*100):.2f}%)")
        self.log(f"⏰ 超时请求: {self.timeout_requests} ({(self.timeout_requests/self.total_requests*100):.2f}%)")
        
        if self.response_times:
            self.log(f"\n📈 响应时间统计:")
            self.log(f"   平均响应时间: {avg_response_time:.3f}秒")
            self.log(f"   最快响应时间: {min_response_time:.3f}秒")
            self.log(f"   最慢响应时间: {max_response_time:.3f}秒")
            self.log(f"   中位数响应时间: {median_response_time:.3f}秒")
        
        if self.errors:
            self.log(f"\n❌ 错误摘要 (显示前5个):")
            for i, error in enumerate(self.errors[:5]):
                self.log(f"   {i+1}. {error}")
            if len(self.errors) > 5:
                self.log(f"   ... 还有 {len(self.errors)-5} 个错误")
        
        self.log("=" * 60)
        self.log(f"📄 详细报告已保存到: {report_file}")
        self.log(f"📁 所有结果文件保存在: {self.result_dir}")

def main():
    parser = argparse.ArgumentParser(description="API 自动化测试脚本")
    parser.add_argument("--api-url", default="http://localhost:8080", 
                       help="API服务地址 (默认: http://localhost:8080)")
    parser.add_argument("--start", type=int, default=1, 
                       help="起始图片编号 (默认: 1)")
    parser.add_argument("--end", type=int, default=200, 
                       help="结束图片编号 (默认: 200)")
    parser.add_argument("--interval", type=int, default=15, 
                       help="请求间隔秒数 (默认: 15)")
    parser.add_argument("--timeout", type=int, default=30, 
                       help="请求超时秒数 (默认: 30)")
    
    args = parser.parse_args()
    
    # 创建测试器并运行
    tester = APITester(
        api_url=args.api_url,
        start_image=args.start,
        end_image=args.end,
        interval=args.interval,
        timeout=args.timeout
    )
    
    try:
        tester.run_test()
    except KeyboardInterrupt:
        tester.log("\n⚠️  测试被用户中断")
        tester.end_time = datetime.now()
        tester.generate_report()
        sys.exit(1)
    except Exception as e:
        tester.log(f"❌ 测试过程中发生错误: {str(e)}", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main()
