#!/bin/bash

# API测试脚本 - 轮询测试200张图片
# 使用方法: ./api_test.sh [API_URL]
# 例如: ./api_test.sh http://localhost:8080

# 配置参数
API_URL=${1:-"http://localhost:8080"}
IMAGE_BASE_URL="http://solve.igmdns.com/img/"
START_IMAGE=1
END_IMAGE=200
INTERVAL=15  # 间隔秒数
TIMEOUT=30   # 超时秒数

# 统计变量
TOTAL_REQUESTS=0
SUCCESS_REQUESTS=0
FAILED_REQUESTS=0
TIMEOUT_REQUESTS=0
START_TIME=$(date +%s)

# 创建结果目录
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULT_DIR="test_results_${TIMESTAMP}"
mkdir -p "$RESULT_DIR"

# 日志文件
LOG_FILE="$RESULT_DIR/api_test.log"
ERROR_FILE="$RESULT_DIR/errors.log"
SUCCESS_FILE="$RESULT_DIR/success.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# 发送API请求
send_request() {
    local image_num=$1
    local image_url="${IMAGE_BASE_URL}$(printf "%02d" $image_num).jpg"
    local api_endpoint="${API_URL}/api/v1/process-image"
    
    # 准备JSON数据
    local json_data="{\"image_url\":\"$image_url\"}"
    
    # 记录开始时间
    local start_time=$(date +%s.%N)
    
    # 发送请求
    local response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        --max-time $TIMEOUT \
        "$api_endpoint" 2>/dev/null)
    
    # 计算响应时间
    local end_time=$(date +%s.%N)
    local response_time=$(echo "$end_time - $start_time" | bc -l)
    
    # 解析响应
    local http_code=$(echo "$response" | tail -n 2 | head -n 1)
    local curl_time=$(echo "$response" | tail -n 1)
    local response_body=$(echo "$response" | head -n -2)
    
    # 检查响应
    if [ -z "$http_code" ] || [ "$http_code" = "000" ]; then
        # 请求超时或连接失败
        print_error "图片 $(printf "%02d" $image_num).jpg - 请求超时或连接失败"
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 图片 $(printf "%02d" $image_num).jpg - 超时" >> "$ERROR_FILE"
        return 2
    elif [ "$http_code" != "200" ]; then
        # HTTP错误
        print_error "图片 $(printf "%02d" $image_num).jpg - HTTP错误: $http_code"
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 图片 $(printf "%02d" $image_num).jpg - HTTP $http_code" >> "$ERROR_FILE"
        return 1
    else
        # 检查API响应
        local api_code=$(echo "$response_body" | grep -o '"code":[0-9]*' | cut -d':' -f2)
        if [ "$api_code" = "200" ]; then
            print_success "图片 $(printf "%02d" $image_num).jpg - 成功 (${response_time}s)"
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 图片 $(printf "%02d" $image_num).jpg - 成功 - ${response_time}s" >> "$SUCCESS_FILE"
            return 0
        else
            local api_message=$(echo "$response_body" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
            print_error "图片 $(printf "%02d" $image_num).jpg - API错误: $api_message"
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 图片 $(printf "%02d" $image_num).jpg - API错误: $api_message" >> "$ERROR_FILE"
            return 1
        fi
    fi
}

# 生成测试报告
generate_report() {
    local end_time=$(date +%s)
    local total_time=$((end_time - START_TIME))
    local success_rate=$(echo "scale=2; $SUCCESS_REQUESTS * 100 / $TOTAL_REQUESTS" | bc -l)
    
    local report_file="$RESULT_DIR/test_report.txt"
    
    cat > "$report_file" << EOF
========================================
API 测试报告
========================================
测试时间: $(date -d @$START_TIME '+%Y-%m-%d %H:%M:%S') - $(date -d @$end_time '+%Y-%m-%d %H:%M:%S')
总耗时: ${total_time}秒
API地址: $API_URL/api/v1/process-image
图片范围: 01.jpg - $(printf "%02d" $END_IMAGE).jpg
请求间隔: ${INTERVAL}秒

统计结果:
- 总请求数: $TOTAL_REQUESTS
- 成功请求: $SUCCESS_REQUESTS (${success_rate}%)
- 失败请求: $FAILED_REQUESTS
- 超时请求: $TIMEOUT_REQUESTS

详细日志:
- 完整日志: $LOG_FILE
- 成功日志: $SUCCESS_FILE  
- 错误日志: $ERROR_FILE
========================================
EOF

    print_info "测试报告已生成: $report_file"
    cat "$report_file"
}

# 主函数
main() {
    print_info "🚀 开始API测试"
    print_info "📍 API地址: $API_URL/api/v1/process-image"
    print_info "🖼️  图片范围: $(printf "%02d" $START_IMAGE).jpg - $(printf "%02d" $END_IMAGE).jpg"
    print_info "⏱️  请求间隔: ${INTERVAL}秒"
    print_info "⏰ 超时时间: ${TIMEOUT}秒"
    print_info "📊 总请求数: $((END_IMAGE - START_IMAGE + 1))"
    print_info "🕐 预计耗时: $((((END_IMAGE - START_IMAGE)) * INTERVAL / 60))分钟"
    print_info "📁 结果目录: $RESULT_DIR"
    echo "========================================"
    
    # 开始测试循环
    for ((i=START_IMAGE; i<=END_IMAGE; i++)); do
        echo ""
        print_info "[$i/$END_IMAGE] 测试图片: $(printf "%02d" $i).jpg"
        
        # 发送请求
        send_request $i
        local result=$?
        
        # 更新统计
        TOTAL_REQUESTS=$((TOTAL_REQUESTS + 1))
        
        case $result in
            0) SUCCESS_REQUESTS=$((SUCCESS_REQUESTS + 1)) ;;
            1) FAILED_REQUESTS=$((FAILED_REQUESTS + 1)) ;;
            2) TIMEOUT_REQUESTS=$((TIMEOUT_REQUESTS + 1))
               FAILED_REQUESTS=$((FAILED_REQUESTS + 1)) ;;
        esac
        
        # 显示当前统计
        print_info "📊 当前统计: 成功 $SUCCESS_REQUESTS, 失败 $FAILED_REQUESTS, 超时 $TIMEOUT_REQUESTS"
        
        # 如果不是最后一个请求，等待间隔时间
        if [ $i -lt $END_IMAGE ]; then
            print_info "⏳ 等待 ${INTERVAL}秒后继续..."
            sleep $INTERVAL
        fi
    done
    
    echo ""
    echo "========================================"
    print_info "🎉 测试完成!"
    
    # 生成报告
    generate_report
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        print_error "bc 未安装，请先安装 bc (用于计算)"
        exit 1
    fi
}

# 脚本入口
echo "API 自动化测试脚本"
echo "===================="

# 检查依赖
check_dependencies

# 运行主函数
main

print_info "测试结果已保存到目录: $RESULT_DIR"
