package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"
)

// 请求结构体
type ImageRequest struct {
	ImageURL string `json:"image_url"`
}

// 响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 测试结果统计
type TestStats struct {
	TotalRequests    int
	SuccessRequests  int
	FailedRequests   int
	TimeoutRequests  int
	StartTime        time.Time
	EndTime          time.Time
	ResponseTimes    []time.Duration
	Errors           []string
	mutex            sync.Mutex // 保护并发访问
}

// 测试配置
type TestConfig struct {
	BaseURL      string
	ImageBaseURL string
	StartImage   int
	EndImage     int
	Interval     time.Duration
	Timeout      time.Duration
	ThreadCount  int
	RoundCount   int
}

func main() {
	// 配置参数
	config := TestConfig{
		BaseURL:      "http://localhost:8080",  // 默认本地地址，可通过环境变量修改
		ImageBaseURL: "http://solve.igmdns.com/img/",
		StartImage:   1,
		EndImage:     100,  // 总共100张图片
		Interval:     15 * time.Second,
		Timeout:      30 * time.Second,
		ThreadCount:  10,   // 10个线程
		RoundCount:   1,    // 只执行1轮，每个线程处理不同图片
	}

	// 从环境变量读取API地址
	if apiURL := os.Getenv("API_URL"); apiURL != "" {
		config.BaseURL = apiURL
	}

	// 初始化统计
	stats := &TestStats{
		StartTime:     time.Now(),
		ResponseTimes: make([]time.Duration, 0),
		Errors:        make([]string, 0),
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: config.Timeout,
	}

	imagesPerThread := (config.EndImage - config.StartImage + 1) / config.ThreadCount
	fmt.Printf("🚀 开始多线程API测试\n")
	fmt.Printf("📍 API地址: %s/api/v1/process-image\n", config.BaseURL)
	fmt.Printf("🖼️  图片范围: %02d.jpg - %02d.jpg (总共%d张)\n", config.StartImage, config.EndImage, config.EndImage-config.StartImage+1)
	fmt.Printf("🧵 线程数量: %d (每线程处理%d张图片)\n", config.ThreadCount, imagesPerThread)
	fmt.Printf("⚡ 并发模式: 无间隔，真正并发处理\n")
	fmt.Printf("⏰ 超时时间: %v\n", config.Timeout)
	totalRequests := config.EndImage - config.StartImage + 1
	fmt.Printf("📊 总请求数: %d (真正并发执行)\n", totalRequests)
	fmt.Printf("🕐 预计耗时: 1.5-3分钟 (取决于最慢线程)\n")
	fmt.Println(strings.Repeat("=", 60))

	// 开始多线程测试
	runMultiThreadTest(client, config, stats)

	// 测试完成，生成报告
	stats.EndTime = time.Now()
	generateReport(stats, config)
}

// 多线程测试函数
func runMultiThreadTest(client *http.Client, config TestConfig, stats *TestStats) {
	var wg sync.WaitGroup

	// 计算每个线程处理的图片范围
	imagesPerThread := (config.EndImage - config.StartImage + 1) / config.ThreadCount

	fmt.Printf("\n🚀 开始并发测试，%d个线程同时处理不同图片\n", config.ThreadCount)

	// 为每个线程创建goroutine，分配不同的图片范围
	for threadID := 1; threadID <= config.ThreadCount; threadID++ {
		wg.Add(1)

		// 计算当前线程的图片范围
		startImg := config.StartImage + (threadID-1)*imagesPerThread
		endImg := startImg + imagesPerThread - 1

		// 确保最后一个线程处理剩余的图片
		if threadID == config.ThreadCount {
			endImg = config.EndImage
		}

		go func(tid, start, end int) {
			defer wg.Done()
			runThreadTest(client, config, stats, tid, start, end)
		}(threadID, startImg, endImg)
	}

	// 等待所有线程完成
	wg.Wait()

	// 显示最终统计
	stats.mutex.Lock()
	fmt.Printf("\n📊 所有线程完成 - 成功: %d, 失败: %d, 超时: %d\n",
		stats.SuccessRequests, stats.FailedRequests, stats.TimeoutRequests)
	stats.mutex.Unlock()
}

// 单个线程的测试函数
func runThreadTest(client *http.Client, config TestConfig, stats *TestStats, threadID, startImg, endImg int) {
	fmt.Printf("[线程%d] 处理图片范围: %02d.jpg - %02d.jpg\n", threadID, startImg, endImg)

	for i := startImg; i <= endImg; i++ {
		imageURL := fmt.Sprintf("%s%02d.jpg", config.ImageBaseURL, i)

		fmt.Printf("[线程%d] 测试图片: %02d.jpg\n", threadID, i)

		// 发送请求
		success, responseTime, err := sendRequest(client, config.BaseURL, imageURL)

		// 线程安全地更新统计
		stats.mutex.Lock()
		stats.TotalRequests++
		stats.ResponseTimes = append(stats.ResponseTimes, responseTime)

		if success {
			stats.SuccessRequests++
			fmt.Printf("✅ [线程%d] 图片%02d.jpg 成功 - 响应时间: %v\n", threadID, i, responseTime)
		} else {
			stats.FailedRequests++
			errorMsg := fmt.Sprintf("线程%d-图片%02d.jpg: %v", threadID, i, err)
			stats.Errors = append(stats.Errors, errorMsg)

			if err != nil && err.Error() == "timeout" {
				stats.TimeoutRequests++
				fmt.Printf("⏰ [线程%d] 图片%02d.jpg 超时 - 响应时间: %v\n", threadID, i, responseTime)
			} else {
				fmt.Printf("❌ [线程%d] 图片%02d.jpg 失败 - 错误: %v\n", threadID, i, err)
			}
		}

		stats.mutex.Unlock()
	}

	fmt.Printf("🏁 [线程%d] 完成所有图片处理\n", threadID)
}

// 发送API请求
func sendRequest(client *http.Client, baseURL, imageURL string) (bool, time.Duration, error) {
	// 准备请求数据
	requestData := ImageRequest{
		ImageURL: imageURL,
	}
	
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return false, 0, fmt.Errorf("JSON编码失败: %v", err)
	}
	
	// 创建请求
	url := fmt.Sprintf("%s/api/v1/process-image", baseURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return false, 0, fmt.Errorf("创建请求失败: %v", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	
	// 发送请求并计时
	startTime := time.Now()
	resp, err := client.Do(req)
	responseTime := time.Since(startTime)
	
	if err != nil {
		if os.IsTimeout(err) {
			return false, responseTime, fmt.Errorf("timeout")
		}
		return false, responseTime, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, responseTime, fmt.Errorf("读取响应失败: %v", err)
	}
	
	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return false, responseTime, fmt.Errorf("解析响应失败: %v", err)
	}
	
	// 检查响应状态
	if resp.StatusCode != 200 || apiResp.Code != 200 {
		return false, responseTime, fmt.Errorf("API返回错误: HTTP %d, Code %d, Message: %s", 
			resp.StatusCode, apiResp.Code, apiResp.Message)
	}
	
	return true, responseTime, nil
}

// 生成测试报告
func generateReport(stats *TestStats, config TestConfig) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📋 测试报告")
	fmt.Println(strings.Repeat("=", 60))
	
	// 基本统计
	fmt.Printf("🕐 测试时间: %v - %v\n", stats.StartTime.Format("2006-01-02 15:04:05"), stats.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("⏱️  总耗时: %v\n", stats.EndTime.Sub(stats.StartTime))
	fmt.Printf("📊 总请求数: %d\n", stats.TotalRequests)
	fmt.Printf("✅ 成功请求: %d (%.2f%%)\n", stats.SuccessRequests, float64(stats.SuccessRequests)/float64(stats.TotalRequests)*100)
	fmt.Printf("❌ 失败请求: %d (%.2f%%)\n", stats.FailedRequests, float64(stats.FailedRequests)/float64(stats.TotalRequests)*100)
	fmt.Printf("⏰ 超时请求: %d (%.2f%%)\n", stats.TimeoutRequests, float64(stats.TimeoutRequests)/float64(stats.TotalRequests)*100)
	
	// 响应时间统计
	if len(stats.ResponseTimes) > 0 {
		var totalTime time.Duration
		var minTime, maxTime time.Duration
		minTime = stats.ResponseTimes[0]
		maxTime = stats.ResponseTimes[0]
		
		for _, t := range stats.ResponseTimes {
			totalTime += t
			if t < minTime {
				minTime = t
			}
			if t > maxTime {
				maxTime = t
			}
		}
		
		avgTime := totalTime / time.Duration(len(stats.ResponseTimes))
		
		fmt.Printf("\n📈 响应时间统计:\n")
		fmt.Printf("   平均响应时间: %v\n", avgTime)
		fmt.Printf("   最快响应时间: %v\n", minTime)
		fmt.Printf("   最慢响应时间: %v\n", maxTime)
	}
	
	// 错误详情
	if len(stats.Errors) > 0 {
		fmt.Printf("\n❌ 错误详情:\n")
		for i, err := range stats.Errors {
			if i < 10 { // 只显示前10个错误
				fmt.Printf("   %d. %s\n", i+1, err)
			}
		}
		if len(stats.Errors) > 10 {
			fmt.Printf("   ... 还有 %d 个错误\n", len(stats.Errors)-10)
		}
	}
	
	// 保存详细报告到文件
	saveDetailedReport(stats, config)
	
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println("🎉 测试完成!")
}

// 保存详细报告到文件
func saveDetailedReport(stats *TestStats, config TestConfig) {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("test_results_%s/api_test_report.json", timestamp)
	
	// 创建目录
	os.MkdirAll(fmt.Sprintf("test_results_%s", timestamp), 0755)
	
	// 准备报告数据
	report := map[string]interface{}{
		"test_config": config,
		"test_stats": stats,
		"summary": map[string]interface{}{
			"total_requests":   stats.TotalRequests,
			"success_requests": stats.SuccessRequests,
			"failed_requests":  stats.FailedRequests,
			"timeout_requests": stats.TimeoutRequests,
			"success_rate":     float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100,
			"test_duration":    stats.EndTime.Sub(stats.StartTime).String(),
		},
	}
	
	// 保存到文件
	jsonData, _ := json.MarshalIndent(report, "", "  ")
	if err := os.WriteFile(filename, jsonData, 0644); err != nil {
		log.Printf("保存报告失败: %v", err)
	} else {
		fmt.Printf("📄 详细报告已保存到: %s\n", filename)
	}
}
