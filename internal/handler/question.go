package handler

import (
	"net/http"
	"go-api-solve/internal/service"

	"github.com/gin-gonic/gin"
)

// QuestionHandler 题目处理器
type QuestionHandler struct {
	questionService *service.QuestionService
}

// NewQuestionHandler 创建新的题目处理器
func NewQuestionHandler(questionService *service.QuestionService) *QuestionHandler {
	return &QuestionHandler{
		questionService: questionService,
	}
}

// ProcessImageRequest 处理图片请求的结构体
type ProcessImageRequest struct {
	ImageURL string `json:"image_url" binding:"required"`
}

// APIResponse 统一的API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ProcessImage 处理图片题目的HTTP处理函数
func (h *QuestionHandler) ProcessImage(c *gin.Context) {
	var req ProcessImageRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证图片URL不为空
	if req.ImageURL == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "图片URL不能为空",
		})
		return
	}

	// 调用业务逻辑处理图片
	responses, err := h.questionService.ProcessImageQuestion(c.Request.Context(), req.ImageURL)
	if err != nil {
		// 检查是否是图片验证错误
		if err.Error() == "图片资源不存在，请重新上传" {
			c.JSON(http.StatusBadRequest, APIResponse{
				Code:    400,
				Message: err.Error(),
			})
			return
		}

		// 检查是否是图片解析异常
		if err.Error() == "图片解析异常，请重新拍摄" {
			c.JSON(http.StatusBadRequest, APIResponse{
				Code:    400,
				Message: err.Error(),
			})
			return
		}

		// 其他服务器内部错误
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "服务器内部错误: " + err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "处理成功",
		Data:    responses,
	})
}

// HealthCheck 健康检查接口
func (h *QuestionHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "服务正常运行",
		Data: gin.H{
			"status": "healthy",
			"service": "go-api-solve",
		},
	})
}
