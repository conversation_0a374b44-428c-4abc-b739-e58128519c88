package repository

import (
	"fmt"
	"go-api-solve/internal/model"
	"gorm.io/gorm"
)

// ModelConfigRepository 模型配置数据访问层
type ModelConfigRepository struct {
	db *gorm.DB
}

// NewModelConfigRepository 创建新的模型配置仓库
func NewModelConfigRepository(db *gorm.DB) *ModelConfigRepository {
	return &ModelConfigRepository{
		db: db,
	}
}

// GetByModelName 根据模型名称获取配置
func (r *ModelConfigRepository) GetByModelName(modelName string) (*model.ModelConfig, error) {
	var config model.ModelConfig
	
	err := r.db.Where("model_name = ? AND is_active = ?", modelName, 1).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("model config not found for model: %s", modelName)
		}
		return nil, fmt.Errorf("failed to get model config: %w", err)
	}
	
	return &config, nil
}

// GetAll 获取所有活跃的模型配置
func (r *ModelConfigRepository) GetAll() ([]model.ModelConfig, error) {
	var configs []model.ModelConfig
	
	err := r.db.Where("is_active = ?", 1).Find(&configs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all model configs: %w", err)
	}
	
	return configs, nil
}

// Create 创建新的模型配置
func (r *ModelConfigRepository) Create(config *model.ModelConfig) error {
	err := r.db.Create(config).Error
	if err != nil {
		return fmt.Errorf("failed to create model config: %w", err)
	}
	
	return nil
}

// Update 更新模型配置
func (r *ModelConfigRepository) Update(config *model.ModelConfig) error {
	err := r.db.Save(config).Error
	if err != nil {
		return fmt.Errorf("failed to update model config: %w", err)
	}
	
	return nil
}

// Delete 删除模型配置（软删除，设置is_active为0）
func (r *ModelConfigRepository) Delete(modelName string) error {
	err := r.db.Model(&model.ModelConfig{}).
		Where("model_name = ?", modelName).
		Update("is_active", 0).Error
	if err != nil {
		return fmt.Errorf("failed to delete model config: %w", err)
	}
	
	return nil
}
