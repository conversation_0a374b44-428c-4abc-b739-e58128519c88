package repository

import (
	"fmt"
	"go-api-solve/internal/model"
	"gorm.io/gorm"
)

// QuestionRepository 题目数据访问层
type QuestionRepository struct {
	db *gorm.DB
}

// NewQuestionRepository 创建新的题目仓库
func NewQuestionRepository(db *gorm.DB) *QuestionRepository {
	return &QuestionRepository{
		db: db,
	}
}

// GetByCacheKeyHash 根据缓存键哈希获取题目
func (r *QuestionRepository) GetByCacheKeyHash(cacheKeyHash string) ([]model.Question, error) {
	var questions []model.Question
	
	err := r.db.Where("cache_key_hash = ?", cacheKeyHash).Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get questions by cache key hash: %w", err)
	}
	
	return questions, nil
}

// Create 创建新题目
func (r *QuestionRepository) Create(question *model.Question) error {
	err := r.db.Create(question).Error
	if err != nil {
		return fmt.Erro<PERSON>("failed to create question: %w", err)
	}
	
	return nil
}

// Update 更新题目
func (r *QuestionRepository) Update(question *model.Question) error {
	err := r.db.Save(question).Error
	if err != nil {
		return fmt.Errorf("failed to update question: %w", err)
	}
	
	return nil
}

// GetByID 根据ID获取题目
func (r *QuestionRepository) GetByID(id int64) (*model.Question, error) {
	var question model.Question
	
	err := r.db.First(&question, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("question not found with id: %d", id)
		}
		return nil, fmt.Errorf("failed to get question by id: %w", err)
	}
	
	return &question, nil
}

// GetByImageURL 根据图片URL获取题目
func (r *QuestionRepository) GetByImageURL(imageURL string) ([]model.Question, error) {
	var questions []model.Question
	
	err := r.db.Where("image_url = ?", imageURL).Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get questions by image URL: %w", err)
	}
	
	return questions, nil
}

// GetAll 获取所有题目（分页）
func (r *QuestionRepository) GetAll(offset, limit int) ([]model.Question, error) {
	var questions []model.Question
	
	err := r.db.Offset(offset).Limit(limit).Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all questions: %w", err)
	}
	
	return questions, nil
}

// Count 获取题目总数
func (r *QuestionRepository) Count() (int64, error) {
	var count int64
	
	err := r.db.Model(&model.Question{}).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count questions: %w", err)
	}
	
	return count, nil
}

// Delete 删除题目
func (r *QuestionRepository) Delete(id int64) error {
	err := r.db.Delete(&model.Question{}, id).Error
	if err != nil {
		return fmt.Errorf("failed to delete question: %w", err)
	}
	
	return nil
}
