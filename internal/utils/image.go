package utils

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// ValidateImageURL 验证图片URL是否有效可访问
func ValidateImageURL(imageURL string) error {
	// 检查URL格式是否有效
	parsedURL, err := url.Parse(imageURL)
	if err != nil {
		return fmt.Errorf("invalid URL format: %w", err)
	}

	// 检查协议是否为http或https
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("unsupported URL scheme: %s", parsedURL.Scheme)
	}

	// 检查是否为图片文件扩展名
	if !isImageExtension(imageURL) {
		return fmt.Errorf("URL does not appear to be an image file")
	}

	// 发送HEAD请求检查资源是否存在
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("HEAD", imageURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置User-Agent避免被某些服务器拒绝
	req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; Go-API-Solve/1.0)")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to access image URL: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("image URL returned status code: %d", resp.StatusCode)
	}

	// 检查Content-Type是否为图片类型
	contentType := resp.Header.Get("Content-Type")
	if !isImageContentType(contentType) {
		return fmt.Errorf("URL does not serve image content, got: %s", contentType)
	}

	return nil
}

// isImageExtension 检查URL是否包含图片文件扩展名
func isImageExtension(imageURL string) bool {
	imageExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
	
	lowerURL := strings.ToLower(imageURL)
	for _, ext := range imageExtensions {
		if strings.Contains(lowerURL, ext) {
			return true
		}
	}
	return false
}

// isImageContentType 检查Content-Type是否为图片类型
func isImageContentType(contentType string) bool {
	imageTypes := []string{
		"image/jpeg",
		"image/jpg", 
		"image/png",
		"image/gif",
		"image/bmp",
		"image/webp",
		"image/svg+xml",
	}
	
	lowerContentType := strings.ToLower(contentType)
	for _, imgType := range imageTypes {
		if strings.Contains(lowerContentType, imgType) {
			return true
		}
	}
	return false
}

// ExtractImageName 从URL中提取图片名称
func ExtractImageName(imageURL string) string {
	parsedURL, err := url.Parse(imageURL)
	if err != nil {
		return ""
	}
	
	// 获取路径的最后一部分作为文件名
	parts := strings.Split(parsedURL.Path, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	
	return ""
}
