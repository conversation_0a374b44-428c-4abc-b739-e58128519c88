package utils

import (
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"regexp"
	"strings"
)

// cleanLineBreaks 清理字符串中的换行符，直接移除
func cleanLineBreaks(text string) string {
	if text == "" {
		return ""
	}
	lineBreakRegex := regexp.MustCompile(`\r\n|\r|\n`)
	return lineBreakRegex.ReplaceAllString(text, "")
}

// FormatQwenData 格式化Qwen返回的数据
// 根据FormatQwenData.md文档的要求进行数据处理
func FormatQwenData(qwenResponse string) (*model.QwenData, error) {
	// 解析JSON响应为临时结构，包含question_num字段和options字段
	var tempData struct {
		QuestionType string            `json:"question_type"`
		QuestionText string            `json:"question_text"`
		QuestionNum  string            `json:"question_num"`
		Options      map[string]string `json:"options"`
	}

	if err := json.Unmarshal([]byte(qwenResponse), &tempData); err != nil {
		return nil, fmt.Errorf("failed to parse Qwen response: %w", err)
	}

	// 验证问题类型
	if !isValidQuestionType(tempData.QuestionType) {
		return nil, fmt.Errorf("图片解析异常，请重新拍摄")
	}

	// 第一步：对题干进行换行符清理和格式清洗
	cleanedText := CleanQuestionText(
		cleanLineBreaks(tempData.QuestionText),
		tempData.QuestionType,
		tempData.QuestionNum,
	)

	// 第二步：处理options格式（包含换行符清理）
	processedOptions := processOptionsWithCleaning(tempData.Options, tempData.QuestionType)

	// 构建最终的QwenData结构
	qwenData := &model.QwenData{
		QuestionType: tempData.QuestionType,
		QuestionText: cleanedText,
		QuestionNum:  tempData.QuestionNum,
		Options:      processedOptions,
	}

	return qwenData, nil
}

// isValidQuestionType 验证问题类型是否有效
func isValidQuestionType(questionType string) bool {
	validTypes := []string{"判断题", "多选题", "单选题"}
	
	// 检查是否为空或无效值
	if questionType == "" {
		return false
	}
	
	// 检查是否在有效类型列表中
	for _, validType := range validTypes {
		if questionType == validType {
			return true
		}
	}
	
	return false
}

// CleanQuestionText 清洗题干文本
// 基于question_num和question_type进行精确清洗，按照S7.md的业务逻辑分步骤处理
func CleanQuestionText(questionText, questionType, questionNum string) string {
	if questionText == "" {
		return ""
	}

	cleaned := questionText

	// 第一步：清洗题目类型部分（基于question_type）
	cleaned = cleanQuestionType(cleaned, questionType)

	// 第二步：清洗序号部分（基于question_num）
	cleaned = cleanQuestionNumber(cleaned, questionNum)

	// 返回清洗后的结果
	return strings.TrimSpace(cleaned)
}

// cleanQuestionType 清洗题目类型部分
func cleanQuestionType(text, questionType string) string {
	if questionType == "" {
		return text
	}

	// 生成题目类型的各种模式
	typePatterns := generateTypePatterns(questionType)

	// 尝试匹配并清除题目类型
	for _, pattern := range typePatterns {
		if strings.HasPrefix(text, pattern) {
			return strings.TrimPrefix(text, pattern)
		}
	}

	return text
}

// cleanQuestionNumber 清洗序号部分
func cleanQuestionNumber(text, questionNum string) string {
	if questionNum == "" {
		return text
	}

	// 生成序号的各种模式
	numberPatterns := generateNumberPatterns(questionNum)

	// 尝试匹配并清除序号
	for _, pattern := range numberPatterns {
		if strings.HasPrefix(text, pattern) {
			return strings.TrimPrefix(text, pattern)
		}
	}

	return text
}

// generateNumberPatterns 生成基于question_num的序号模式
func generateNumberPatterns(questionNum string) []string {
	if questionNum == "" {
		return []string{}
	}

	patterns := []string{}

	// 原始序号模式
	patterns = append(patterns, []string{
		questionNum + ".",
		questionNum + "、",
		questionNum + "。",
		questionNum + " ",
		questionNum + "  ", // 两个空格的情况
	}...)

	// 处理带前导零的情况（如"8" -> "08"）
	if len(questionNum) == 1 {
		paddedNum := "0" + questionNum
		patterns = append(patterns, []string{
			paddedNum + ".",
			paddedNum + "、",
			paddedNum + "。",
			paddedNum + " ",
			paddedNum + "  ", // 两个空格的情况
		}...)
	}

	// 处理去掉前导零的情况（如"08" -> "8"）
	if len(questionNum) == 2 && questionNum[0] == '0' {
		trimmedNum := questionNum[1:]
		patterns = append(patterns, []string{
			trimmedNum + ".",
			trimmedNum + "、",
			trimmedNum + "。",
			trimmedNum + " ",
			trimmedNum + "  ", // 两个空格的情况
		}...)
	}

	return patterns
}

// generateTypePatterns 生成基于question_type的题目类型模式
func generateTypePatterns(questionType string) []string {
	if questionType == "" {
		return []string{}
	}

	// 简化的题目类型模式，只处理标准情况
	patterns := []string{
		"(" + questionType + ")",     // (判断题)
		"（" + questionType + "）",    // （判断题）
		questionType,                 // 判断题
	}

	return patterns
}

// processOptionsWithCleaning 处理options格式，包含换行符清理和格式转换
func processOptionsWithCleaning(options map[string]string, questionType string) map[string]string {
	processedOptions := make(map[string]string)

	for key, value := range options {
		// 清理换行符
		cleanedKey := cleanLineBreaks(key)
		cleanedValue := cleanLineBreaks(value)

		// 处理判断题的特殊格式：如 "Y:正确" -> "Y": "正确"
		if strings.Contains(cleanedKey, ":") {
			if optionKey, optionValue := parseColonSeparatedOption(cleanedKey); optionKey != "" {
				// 优先使用解析出的值，如果value不为空则使用value
				finalValue := optionValue
				if cleanedValue != "" {
					finalValue = cleanedValue
				}
				if finalValue != "" {
					processedOptions[optionKey] = finalValue
				}
				continue
			}
		}

		// 处理标准格式：直接使用key-value，过滤空值
		if cleanedValue != "" {
			processedOptions[cleanedKey] = cleanedValue
		}
	}

	return processedOptions
}



// parseColonSeparatedOption 解析冒号分隔的选项格式
func parseColonSeparatedOption(key string) (optionKey, optionValue string) {
	parts := strings.SplitN(key, ":", 2)
	if len(parts) == 2 {
		return strings.TrimSpace(parts[0]), strings.TrimSpace(parts[1])
	}
	return "", ""
}

// setQuestionOptions 统一设置Question的选项字段
func setQuestionOptions(question *model.Question, options map[string]string) {
	for key, value := range options {
		if value != "" {
			switch key {
			case "A":
				question.OptionA = &value
			case "B":
				question.OptionB = &value
			case "C":
				question.OptionC = &value
			case "D":
				question.OptionD = &value
			case "Y":
				question.OptionY = &value
			case "N":
				question.OptionN = &value
			}
		}
	}
}

// ConvertQwenToQuestion 将Qwen数据转换为Question模型
func ConvertQwenToQuestion(qwenData *model.QwenData, imageURL string, qwenRaw string) *model.Question {
	question := &model.Question{
		QuestionType: qwenData.QuestionType,
		QuestionText: qwenData.QuestionText,
		UserImage:    imageURL,
		IsVerified:   0,
	}

	// 设置选项 - 直接使用Options，避免重复转换
	setQuestionOptions(question, qwenData.Options)

	// 设置原始数据
	var qwenRawData model.JSONField
	if err := json.Unmarshal([]byte(qwenRaw), &qwenRawData); err == nil {
		question.QwenRaw = qwenRawData
	}

	// 设置解析后的数据
	qwenParsedData := make(model.JSONField)
	qwenParsedBytes, _ := json.Marshal(qwenData)
	json.Unmarshal(qwenParsedBytes, &qwenParsedData)
	question.QwenParsed = qwenParsedData

	return question
}

// BuildOptionsMap 构建选项映射
func BuildOptionsMap(question *model.Question) map[string]string {
	options := make(map[string]string)
	
	if question.OptionA != nil {
		options["A"] = *question.OptionA
	}
	if question.OptionB != nil {
		options["B"] = *question.OptionB
	}
	if question.OptionC != nil {
		options["C"] = *question.OptionC
	}
	if question.OptionD != nil {
		options["D"] = *question.OptionD
	}
	if question.OptionY != nil {
		options["Y"] = *question.OptionY
	}
	if question.OptionN != nil {
		options["N"] = *question.OptionN
	}
	
	return options
}

// ConvertToQuestionResponse 将Question模型转换为响应格式
func ConvertToQuestionResponse(question *model.Question) *model.QuestionResponse {
	response := &model.QuestionResponse{
		QuestionType: question.QuestionType,
		QuestionText: question.QuestionText,
		Options:      BuildOptionsMap(question),
		UserImage:    question.UserImage,
		IsVerified:   fmt.Sprintf("%d", question.IsVerified),
	}

	// 设置问题对应的图片名称（如果存在）
	if question.ImageURL != nil {
		response.ImageURL = *question.ImageURL
	}
	// 注意：ImageURL字段默认为空字符串，无需显式设置

	// 设置解析
	if question.Analysis != nil {
		response.Analysis = *question.Analysis
	}

	// 设置答案
	if question.Answer != nil {
		answerMap := make(map[string]string)
		for key, value := range question.Answer {
			if strValue, ok := value.(string); ok {
				answerMap[key] = strValue
			}
		}
		response.Answer = answerMap
	}

	return response
}
