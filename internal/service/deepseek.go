package service

import (
	"context"
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/pkg/ai"
)

// DeepseekService DeepSeek服务
type DeepseekService struct {
	deepseekClient  *ai.DeepseekClient
	modelConfigRepo *repository.ModelConfigRepository
}

// NewDeepseekService 创建新的DeepSeek服务
func NewDeepseekService(deepseekClient *ai.DeepseekClient, modelConfigRepo *repository.ModelConfigRepository) *DeepseekService {
	return &DeepseekService{
		deepseekClient:  deepseekClient,
		modelConfigRepo: modelConfigRepo,
	}
}

// ProcessQuestion 处理题目，调用DeepSeek模型进行分析
func (s *DeepseekService) ProcessQuestion(ctx context.Context, qwenParsedData string) (*model.DeepseekData, string, error) {
	// 获取DeepSeek模型配置
	config, err := s.modelConfigRepo.GetByModelName("deepseek-chat")
	if err != nil {
		return nil, "", fmt.Errorf("failed to get deepseek model config: %w", err)
	}

	// 调用DeepSeek API
	response, err := s.deepseekClient.CallDeepseekChat(ctx, qwenParsedData, config)
	if err != nil {
		return nil, "", fmt.Errorf("failed to call deepseek API: %w", err)
	}

	// 提取响应内容
	content, err := ai.ExtractContentFromDeepseekResponse(response)
	if err != nil {
		return nil, "", fmt.Errorf("failed to extract content from deepseek response: %w", err)
	}

	// 解析DeepSeek返回的数据
	var deepseekData model.DeepseekData
	if err := json.Unmarshal([]byte(content), &deepseekData); err != nil {
		return nil, "", fmt.Errorf("failed to parse deepseek response: %w", err)
	}

	// 返回解析的数据和原始响应
	rawResponse, _ := json.Marshal(response)
	return &deepseekData, string(rawResponse), nil
}
