package redis

import (
	"context"
	"fmt"
	"log"
	"time"

	"go-api-solve/internal/config"

	"github.com/redis/go-redis/v9"
)

// Client 全局Redis客户端
var Client *redis.Client

// InitRedis 初始化Redis连接
func InitRedis(cfg *config.Config) error {
	// 创建Redis客户端
	Client = redis.NewClient(&redis.Options{
		Addr:     cfg.GetRedisAddr(),
		Username: cfg.Redis.Username,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
		
		// 连接池配置
		PoolSize:     10,               // 连接池大小
		MinIdleConns: 5,                // 最小空闲连接数
		MaxIdleConns: 10,               // 最大空闲连接数
		PoolTimeout:  30 * time.Second, // 连接池超时时间
		
		// 超时配置
		DialTimeout:  5 * time.Second,  // 连接超时
		ReadTimeout:  3 * time.Second,  // 读取超时
		WriteTimeout: 3 * time.Second,  // 写入超时
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := Client.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Println("Redis connected successfully")
	return nil
}

// CloseRedis 关闭Redis连接
func CloseRedis() error {
	if Client == nil {
		return nil
	}

	if err := Client.Close(); err != nil {
		return fmt.Errorf("failed to close Redis connection: %w", err)
	}

	log.Println("Redis connection closed")
	return nil
}

// GetClient 获取Redis客户端实例
func GetClient() *redis.Client {
	return Client
}

// Set 设置键值对
func Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return Client.Set(ctx, key, value, expiration).Err()
}

// Get 获取值
func Get(ctx context.Context, key string) (string, error) {
	return Client.Get(ctx, key).Result()
}

// Exists 检查键是否存在
func Exists(ctx context.Context, key string) (bool, error) {
	result, err := Client.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}
	return result > 0, nil
}

// Delete 删除键
func Delete(ctx context.Context, key string) error {
	return Client.Del(ctx, key).Err()
}

// SetJSON 设置JSON值
func SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return Client.Set(ctx, key, value, expiration).Err()
}

// GetJSON 获取JSON值
func GetJSON(ctx context.Context, key string) (string, error) {
	return Client.Get(ctx, key).Result()
}
